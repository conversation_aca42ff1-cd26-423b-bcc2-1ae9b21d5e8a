import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:gsheets/gsheets.dart';
import 'package:retry/retry.dart';
import 'package:songanh/config/gsheet.dart';
import 'package:songanh/page/history_page.dart';
import 'package:songanh/page/home_page.dart';
import 'package:songanh/routes.dart';
import 'package:songanh/theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  final remoteConfig = FirebaseRemoteConfig.instance;
  await remoteConfig.setConfigSettings(RemoteConfigSettings(
    fetchTimeout: const Duration(minutes: 1),
    minimumFetchInterval: const Duration(hours: 1),
  ));
// await remoteConfig.setDefaults(const {
//     "value": '123',
//     "value2": '3.14159',
//     "value3": 'true',
//     "value4": "Hello, world!",
// });
  await retry<bool>(
    // Make a GET request
    () => FirebaseRemoteConfig.instance
        .fetchAndActivate()
        .timeout(const Duration(seconds: 60)),
    // Retry on FirebaseException or TimeoutException
    retryIf: (e) => e is FirebaseException || e is TimeoutException,
  );
  await FirebaseRemoteConfig.instance.setDefaults(const {
    "example_param_1": 42,
    "example_param_2": 3.14159,
    "example_param_3": true,
    "testapp": "Hello, world!",
  });

  final spreadsheetId = remoteConfig.getString('spreadsheetId');
  final credentialsJson = remoteConfig.getValue('keygsheet');
  GsheetConfig.gsheets = GSheets(credentialsJson.asString());
  GsheetConfig.spreadsheet =
      await GsheetConfig.gsheets.spreadsheet(spreadsheetId);

  runApp(MyApp(
    spreadsheetId: spreadsheetId,
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key, required this.spreadsheetId});
  static bool firstTimeOneTab = false;
  static bool firstTimeSecondTab = false;
  final String spreadsheetId;
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      locale: const Locale('vi', 'VN'),
      supportedLocales: const [
        Locale('vi', 'VN'),
        Locale('en', 'US'), 
      ],
      localizationsDelegates: const [
        // Các delegate mặc định của Flutter
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      title: 'Flutter Demo',
      theme: AppTheme.theme,
      home: FutureBuilder<List<Worksheet>>(
          future: GsheetConfig.initListWork(spreadsheetId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                child: CircularProgressIndicator(),
              );
            }
            if (snapshot.connectionState == ConnectionState.done) {
              return MyHomePage(
                works: snapshot.data ?? [],
              );
            }
            return SizedBox();
          }),
    );
  }
}

class MyHomePage extends StatelessWidget {
  const MyHomePage({super.key, required this.works});
  final List<Worksheet> works;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      floatingActionButton: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: FloatingActionButton.extended(
          backgroundColor: Colors.transparent,
          elevation: 0,
          icon: Icon(Icons.map_outlined, color: Colors.white),
          label: Text(
            'Tổng quan sơ đồ',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          onPressed: () {
            AppNavigator.pushAllRoom(context, works.first);
          },
        ),
      ),
      body: DefaultTabController(
        initialIndex: 0,
        length: works.length,
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.white,
            title: Text(
              'Quản lý phòng khách sạn',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w700,
              ),
            ),
            centerTitle: true,
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(60),
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: TabBar(
                  dividerHeight: 0,
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicator: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary,
                        Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.grey.shade600,
                  labelStyle: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  tabs: [
                    ...List.generate(works.length, (i) {
                      final tab = works[i].title;
                      return Tab(
                        height: 44,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              i == 0 ? Icons.home_outlined : Icons.history_outlined,
                              size: 18,
                            ),
                            SizedBox(width: 8),
                            Text(tab),
                          ],
                        ),
                      );
                    })
                  ],
                ),
              ),
            ),
          ),
          body: TabBarView(children: [
            HomePage(
              wSheet: works.first,
            ),
            HistoryPage(
              wSheet: works.last,
            )
          ]),
        ),
      ),
    );
  }
}
