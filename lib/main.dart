import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:gsheets/gsheets.dart';
import 'package:retry/retry.dart';
import 'package:songanh/config/gsheet.dart';
import 'package:songanh/page/history_page.dart';
import 'package:songanh/page/home_page.dart';
import 'package:songanh/routes.dart';
import 'package:songanh/theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  final remoteConfig = FirebaseRemoteConfig.instance;
  await remoteConfig.setConfigSettings(RemoteConfigSettings(
    fetchTimeout: const Duration(minutes: 1),
    minimumFetchInterval: const Duration(hours: 1),
  ));
// await remoteConfig.setDefaults(const {
//     "value": '123',
//     "value2": '3.14159',
//     "value3": 'true',
//     "value4": "Hello, world!",
// });
  await retry<bool>(
    // Make a GET request
    () => FirebaseRemoteConfig.instance
        .fetchAndActivate()
        .timeout(const Duration(seconds: 60)),
    // Retry on FirebaseException or TimeoutException
    retryIf: (e) => e is FirebaseException || e is TimeoutException,
  );
  await FirebaseRemoteConfig.instance.setDefaults(const {
    "example_param_1": 42,
    "example_param_2": 3.14159,
    "example_param_3": true,
    "testapp": "Hello, world!",
  });

  final spreadsheetId = remoteConfig.getString('spreadsheetId');
  final credentialsJson = remoteConfig.getValue('keygsheet');
  GsheetConfig.gsheets = GSheets(credentialsJson.asString());
  GsheetConfig.spreadsheet =
      await GsheetConfig.gsheets.spreadsheet(spreadsheetId);

  runApp(MyApp(
    spreadsheetId: spreadsheetId,
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key, required this.spreadsheetId});
  static bool firstTimeOneTab = false;
  static bool firstTimeSecondTab = false;
  final String spreadsheetId;
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      locale: const Locale('vi', 'VN'),
      supportedLocales: const [
        Locale('vi', 'VN'),
        Locale('en', 'US'), 
      ],
      localizationsDelegates: const [
        // Các delegate mặc định của Flutter
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      title: 'Flutter Demo',
      theme: AppTheme.theme,
      home: FutureBuilder<List<Worksheet>>(
          future: GsheetConfig.initListWork(spreadsheetId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                child: CircularProgressIndicator(),
              );
            }
            if (snapshot.connectionState == ConnectionState.done) {
              return MyHomePage(
                works: snapshot.data ?? [],
              );
            }
            return SizedBox();
          }),
    );
  }
}

class MyHomePage extends StatelessWidget {
  const MyHomePage({super.key, required this.works});
  final List<Worksheet> works;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      floatingActionButton: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFFD4AF37), // Luxury Gold
              Color(0xFFFFD700), // Bright Gold
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Color(0xFFD4AF37).withValues(alpha: 0.4),
              blurRadius: 12,
              offset: Offset(0, 6),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: Offset(0, 10),
            ),
          ],
        ),
        child: FloatingActionButton.extended(
          backgroundColor: Colors.transparent,
          elevation: 0,
          icon: Container(
            padding: EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.map_outlined,
              color: Color(0xFF1A237E),
              size: 20,
            ),
          ),
          label: Text(
            'Sơ đồ tổng quan',
            style: GoogleFonts.playfairDisplay(
              color: Color(0xFF1A237E),
              fontWeight: FontWeight.w700,
              fontSize: 16,
              letterSpacing: 0.5,
            ),
          ),
          onPressed: () {
            AppNavigator.pushAllRoom(context, works.first);
          },
        ),
      ),
      body: DefaultTabController(
        initialIndex: 0,
        length: works.length,
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.white,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white,
                    Color(0xFFFFF8E1).withValues(alpha: 0.3),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
            title: Column(
              children: [
                Text(
                  'LUXURY HOTEL',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFFD4AF37),
                    letterSpacing: 2.0,
                  ),
                ),
                Text(
                  'Quản lý phòng',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF1A237E),
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            centerTitle: true,
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(80),
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: TabBar(
                  dividerHeight: 0,
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicator: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFFD4AF37),
                        Color(0xFFFFD700),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(18),
                    boxShadow: [
                      BoxShadow(
                        color: Color(0xFFD4AF37).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  labelColor: Color(0xFF1A237E),
                  unselectedLabelColor: Colors.grey.shade600,
                  labelStyle: GoogleFonts.playfairDisplay(
                    fontWeight: FontWeight.w700,
                    fontSize: 15,
                    letterSpacing: 0.5,
                  ),
                  unselectedLabelStyle: GoogleFonts.inter(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  tabs: [
                    ...List.generate(works.length, (i) {
                      final tab = works[i].title;
                      return Tab(
                        height: 50,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: i == 0
                                      ? Color(0xFF1A237E).withValues(alpha: 0.1)
                                      : Colors.grey.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Icon(
                                  i == 0 ? Icons.hotel_outlined : Icons.history_outlined,
                                  size: 16,
                                ),
                              ),
                              SizedBox(width: 8),
                              Flexible(child: Text(tab)),
                            ],
                          ),
                        ),
                      );
                    })
                  ],
                ),
              ),
            ),
          ),
          body: TabBarView(children: [
            HomePage(
              wSheet: works.first,
            ),
            HistoryPage(
              wSheet: works.last,
            )
          ]),
        ),
      ),
    );
  }
}
