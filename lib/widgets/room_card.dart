import 'package:flutter/material.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/widgets/expanded_floor_card.dart';

class RoomCard extends StatefulWidget {
  const RoomCard({super.key, required this.room, required this.isOpen});
  final Room room;
  final Function() isOpen;

  @override
  State<RoomCard> createState() => _RoomCardState();
}

class _RoomCardState extends State<RoomCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getRoomColor() {
    if (widget.room.isActive == 'TRUE') {
      return Color(0xFFFFF8E1); // Warm cream
    }
    return Colors.white;
  }

  Color _getBorderColor() {
    if (widget.room.isActive == 'TRUE') {
      return Color(0xFFD4AF37); // Luxury gold
    }
    return Color(0xFFC0C0C0).withValues(alpha: 0.5); // Premium silver
  }

  List<BoxShadow> _getRoomShadow() {
    if (widget.room.isActive == 'TRUE') {
      return [
        BoxShadow(
          color: Color(0xFFD4AF37).withValues(alpha: 0.2),
          blurRadius: 12,
          offset: Offset(0, 6),
          spreadRadius: 1,
        ),
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 20,
          offset: Offset(0, 10),
        ),
      ];
    }
    return [
      BoxShadow(
        color: Colors.grey.withValues(alpha: 0.1),
        blurRadius: 8,
        offset: Offset(0, 4),
      ),
    ];
  }

  Widget _buildRoomTypeChip() {
    final roomType = widget.room.type?.replaceAll(' ', '').toUpperCase();

    if (roomType == RoomType.vipstandard.name.toUpperCase()) {
      return _buildChip('VIP', Color(0xFFFFD700), Color(0xFF1A237E));
    } else if (roomType == RoomType.deluxe.name.toUpperCase()) {
      return _buildChip('DELUXE', Color(0xFF3949AB).withValues(alpha: 0.1), Color(0xFF3949AB));
    } else if (roomType == RoomType.suite.name.toUpperCase()) {
      return _buildChip('SUITE', Color(0xFF6A1B9A).withValues(alpha: 0.1), Color(0xFF6A1B9A));
    }

    return SizedBox.shrink();
  }

  Widget _buildChip(String text, Color backgroundColor, Color textColor) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: textColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: textColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 9,
          fontWeight: FontWeight.w700,
          letterSpacing: 0.8,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        _animationController.forward();
      },
      onTapUp: (_) {
        _animationController.reverse();
        widget.isOpen();
      },
      onTapCancel: () {
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 96,
              height: 96,
              margin: EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: _getRoomColor(),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: _getBorderColor(),
                  width: 2,
                ),
                boxShadow: _getRoomShadow(),
              ),
              child: Stack(
                children: [
                  // Room ID in center
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: widget.room.isActive == 'TRUE'
                                ? Color(0xFFD4AF37).withValues(alpha: 0.2)
                                : Colors.grey.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            widget.room.roomId ?? '',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w800,
                              color: widget.room.isActive == 'TRUE'
                                  ? Color(0xFF1A237E)
                                  : Color(0xFF37474F),
                              fontSize: 18,
                            ),
                          ),
                        ),
                        if (widget.room.isActive == 'TRUE')
                          Container(
                            margin: EdgeInsets.only(top: 6),
                            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Color(0xFF2E7D32), Color(0xFF4CAF50)],
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0xFF2E7D32).withValues(alpha: 0.3),
                                  blurRadius: 4,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Text(
                              'OCCUPIED',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                                fontWeight: FontWeight.w700,
                                letterSpacing: 0.8,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Room type chip
                  Positioned(
                    top: 8,
                    right: 8,
                    child: _buildRoomTypeChip(),
                  ),

                  // Luxury indicator
                  if (widget.room.isActive == 'TRUE')
                    Positioned(
                      top: 6,
                      left: 6,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Color(0xFFD4AF37), Color(0xFFFFD700)],
                          ),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Color(0xFFD4AF37).withValues(alpha: 0.5),
                              blurRadius: 6,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.star,
                          color: Color(0xFF1A237E),
                          size: 10,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
 