import 'package:flutter/material.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/widgets/expanded_floor_card.dart';

class RoomCard extends StatefulWidget {
  const RoomCard({super.key, required this.room, required this.isOpen});
  final Room room;
  final Function() isOpen;

  @override
  State<RoomCard> createState() => _RoomCardState();
}

class _RoomCardState extends State<RoomCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getRoomColor() {
    if (widget.room.isActive == 'TRUE') {
      return Colors.green.shade50;
    }
    return Colors.white;
  }

  Color _getBorderColor() {
    if (widget.room.isActive == 'TRUE') {
      return Colors.green.shade300;
    }
    return Colors.grey.shade200;
  }

  Widget _buildRoomTypeChip() {
    final roomType = widget.room.type?.replaceAll(' ', '').toUpperCase();

    if (roomType == RoomType.vipstandard.name.toUpperCase()) {
      return _buildChip('VIP', Colors.amber.shade100, Colors.amber.shade800);
    } else if (roomType == RoomType.deluxe.name.toUpperCase()) {
      return _buildChip('DELUXE', Colors.blue.shade100, Colors.blue.shade800);
    } else if (roomType == RoomType.suite.name.toUpperCase()) {
      return _buildChip('SUITE', Colors.purple.shade100, Colors.purple.shade800);
    }

    return SizedBox.shrink();
  }

  Widget _buildChip(String text, Color backgroundColor, Color textColor) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: textColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 10,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        _animationController.forward();
      },
      onTapUp: (_) {
        _animationController.reverse();
        widget.isOpen();
      },
      onTapCancel: () {
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 96,
              height: 96,
              margin: EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: _getRoomColor(),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _getBorderColor(),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: widget.room.isActive == 'TRUE'
                        ? Colors.green.withValues(alpha: 0.2)
                        : Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Room ID in center
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          widget.room.roomId ?? '',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: widget.room.isActive == 'TRUE'
                                ? Colors.green.shade800
                                : Colors.grey.shade700,
                          ),
                        ),
                        if (widget.room.isActive == 'TRUE')
                          Container(
                            margin: EdgeInsets.only(top: 4),
                            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green.shade600,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Đang sử dụng',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Room type chip
                  Positioned(
                    top: 8,
                    right: 8,
                    child: _buildRoomTypeChip(),
                  ),

                  // Active indicator
                  if (widget.room.isActive == 'TRUE')
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: Colors.green.shade600,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.green.withValues(alpha: 0.4),
                              blurRadius: 4,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
 