import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/routes.dart';

enum RoomType {
  deluxe,
  suite,
  vipstandard

}

class ExpandedFloorCard extends StatefulWidget {
  const ExpandedFloorCard({super.key, required this.rooms});
  final List<Room> rooms;

  @override
  State<ExpandedFloorCard> createState() => _ExpandedFloorCardState();
}

class _ExpandedFloorCardState extends State<ExpandedFloorCard> {
  late final ValueNotifier<Item> valItem;
  @override
  void initState() {
    valItem = ValueNotifier(
      Item(room: widget.rooms),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: valItem,
        builder: (context, vRoom, child) {
          final roomActive = vRoom.room
              .where((final r) => r.isActive == 'TRUE');
          final total = roomActive.fold(
              0, (l, r) => l + (int.tryParse(r.gia ?? '') ?? 0));
          return _buildFloor(valItem, roomActive, total);
        });
  }

  ValueListenableBuilder<Item> _buildFloor(
      ValueNotifier<Item> valItem, Iterable<Room> roomActive, int total) {
    return ValueListenableBuilder(
      valueListenable: valItem,
      builder: (context, vItem, child) {
        return Container(
          width: MediaQuery.sizeOf(context).width - 24,
          margin: EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 20,
                offset: Offset(0, 8),
                spreadRadius: 2,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 40,
                offset: Offset(0, 16),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header sang trọng với gradient
              Container(
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(0xFF1A237E),
                      Color(0xFF3949AB),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0xFF1A237E).withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Color(0xFFD4AF37),
                                Color(0xFFFFD700),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Color(0xFFD4AF37).withValues(alpha: 0.4),
                                blurRadius: 8,
                                offset: Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.domain,
                            color: Color(0xFF1A237E),
                            size: 28,
                          ),
                        ),
                        SizedBox(width: 12),
                        Text(
                          'TẦNG ${vItem.room.first.floor}',
                          style: GoogleFonts.playfairDisplay(
                            color: Colors.white,
                            fontWeight: FontWeight.w800,
                            fontSize: 24,
                            letterSpacing: 1.2,
                          ),
                        ),
                      ],
                    ),
                    if (roomActive.isNotEmpty)
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '+ ${NumberFormat('#,###', 'vi_VN').format(total)} VND',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                            fontSize: 14,
                          ),
                        ),
                      ),
                  ],
                ),
              ),

              // Content
              Container(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Status row
                    Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: roomActive.isNotEmpty ? Colors.green : Colors.grey.shade400,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            roomActive.isNotEmpty
                                ? '${roomActive.length} phòng đang hoạt động'
                                : 'Hiện tại còn phòng trống',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: roomActive.isNotEmpty ? Colors.green.shade700 : Colors.grey.shade600,
                            ),
                          ),
                        ),
                        TextButton.icon(
                          onPressed: () {
                            AppNavigator.pushDetailFloor(context, widget.rooms);
                          },
                          icon: Icon(Icons.visibility_outlined, size: 16),
                          label: Text('Xem chi tiết'),
                          style: TextButton.styleFrom(
                            foregroundColor: Theme.of(context).colorScheme.primary,
                            textStyle: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                      ],
                    ),

                    if (roomActive.isNotEmpty) ...[
                      SizedBox(height: 16),
                      ExpansionTile(
                        tilePadding: EdgeInsets.zero,
                        childrenPadding: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        collapsedShape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        backgroundColor: Colors.grey.shade50,
                        collapsedBackgroundColor: Colors.transparent,
                        title: Text(
                          'Chi tiết phòng hoạt động',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        onExpansionChanged: (value) {
                          valItem.value = vItem.copyWith(
                            isExpanded: !vItem.isExpanded,
                          );
                        },
                        children: [
                          Container(
                            padding: EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(12),
                                bottomRight: Radius.circular(12),
                              ),
                            ),
                            child: Column(
                              children: [
                                for (int i = 0; i < roomActive.length; i++) ...[
                                  _buildRoomListItem(context, roomActive.elementAt(i)),
                                  if (i < roomActive.length - 1)
                                    Divider(height: 24, color: Colors.grey.shade300),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildRoomListItem(BuildContext context, Room room) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  room.roomId ?? '',
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    color: Colors.green.shade800,
                  ),
                ),
              ),
              SizedBox(width: 8),
              _buildRoomTypeChip(room.type),
              Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.shade600,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '+ ${NumberFormat('#,###', 'vi_VN').format(int.tryParse(room.gia ?? '') ?? 0)} VND',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          if (room.timeIn?.isNotEmpty ?? false)
            _buildTimeInfo('Nhận phòng', room.timeIn ?? ''),
          if (room.timeOut?.isNotEmpty ?? false) ...[
            SizedBox(height: 4),
            _buildTimeInfo('Trả phòng', room.timeOut ?? ''),
          ],
        ],
      ),
    );
  }

  Widget _buildRoomTypeChip(String? type) {
    final roomType = type?.replaceAll(' ', '').toUpperCase();

    if (roomType == RoomType.vipstandard.name.toUpperCase()) {
      return _buildChip('VIP', Colors.amber.shade100, Colors.amber.shade800);
    } else if (roomType == RoomType.deluxe.name.toUpperCase()) {
      return _buildChip('DELUXE', Colors.blue.shade100, Colors.blue.shade800);
    } else if (roomType == RoomType.suite.name.toUpperCase()) {
      return _buildChip('SUITE', Colors.purple.shade100, Colors.purple.shade800);
    }

    return SizedBox.shrink();
  }

  Widget _buildChip(String text, Color backgroundColor, Color textColor) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTimeInfo(String label, String time) {
    DateTime dateTime = DateFormat('HH:mm dd/MM/yyyy').parse(time).toLocal();
    return Row(
      children: [
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            DateFormat('HH:mm - dd/MM/yyyy').format(dateTime),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade800,
            ),
          ),
        ),
      ],
    );
  }
}

class Item {
  final List<Room> room;
  bool isExpanded;

  Item({required this.room, this.isExpanded = false});

  Item copyWith({List<Room>? room, bool? isExpanded}) {
    return Item(
      room: room ?? this.room,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}
